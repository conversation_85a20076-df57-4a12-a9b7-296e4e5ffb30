<?php

namespace App\Http\Controllers\Admin;

use Inertia\Inertia;
use App\Models\MarketplacePayment;
use App\Http\Controllers\Controller;
use Domain\Payment\Payment;
use Domain\Payment\Requests\AdminPaymentFilterRequest;
use Inertia\Response;


class AdminPaymentController extends Controller
{

    /*********************************************************************
     * ADMIN PAYMENT INDEX PAGE
     *********************************************************************
     * Display filtered marketplace payments in admin panel.
     *
     * @param AdminPaymentFilterRequest $request
     * @return Response
     *********************************************************************/
    public function index(AdminPaymentFilterRequest $request): Response
    {

        // Get filters
        $filters = $request->getValidatedFilters();


        // Fetch payments
        $paymentData = (new Payment())->getPaymentsForAdmin($filters);


        // Render response
        return Inertia::render('Admin/Payments/Index', [
            'payments' => $paymentData['payments'],
            'totals'   => $paymentData['totals'],
            'filters'  => [
                'search'       => $filters['searchTerm'],
                'sort_by'      => $filters['sortField'],
                'sort_dir'     => $filters['sortOrder'],
                'status'       => $filters['status'],
                'preset_range' => $filters['preset_range'],
                'start_date'   => $filters['start_date'],
                'end_date'     => $filters['end_date'],
            ],
        ]);
    }




    /*********************************************************************
     * ADMIN PAYMENT SHOW PAGE
     *********************************************************************
     *
     * Retrieves and displays detailed payment information including
     * user and order details.
     *
     * @param MarketplacePayment $payment - The payment to display
     * @return Response - Admin payment show page
     *
     *********************************************************************/
    public function show(MarketplacePayment $payment)
    {
        // -----------------------
        // Load Relationships
        $payment->load([
            'user',
            'order' => fn($query) => $query->withCount('orderItems')
        ]);


        // -----------------------
        // Return Inertia Response
        return Inertia::render('Admin/Payments/Show', [
            'payment' => $payment,
        ]);
    }
}
