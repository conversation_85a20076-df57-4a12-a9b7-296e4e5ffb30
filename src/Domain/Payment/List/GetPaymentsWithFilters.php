<?php

namespace Domain\Payment\List;

use App\Models\MarketplacePayment;
use App\Traits\Filters\DateFilterTrait;
use Domain\Payment\Enum\PaymentStatus;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;


class GetPaymentsWithFilters
{
    use DateFilterTrait;


    /*********************************************************************
     * GET PAYMENTS WITH TOTALS
     *********************************************************************
     *
     * Returns both paginated payments and status totals
     *
     *
     * @param array $filters - Filter parameters
     * @return array - ['payments' => LengthAwarePaginator, 'totals' => array]
     *
     *********************************************************************/
    public function __invoke(array $filters): array
    {
        // -----------------------
        // Build Base Query With All Filters
        $baseQuery = $this->buildBaseQuery($filters);


        // -----------------------
        // Calculate Status Totals From Base Query
        $totals = $this->calculateTotals(clone $baseQuery);


        // -----------------------
        // Get Paginated Results From Base Query
        $payments = $this->getPaginatedResults(clone $baseQuery, $filters);


        return [
            'payments' => $payments,
            'totals'   => $totals,
        ];
    }




    /*********************************************************************
     * BUILD BASE QUERY
     *********************************************************************
     *
     * Creates the base query with all filters applied.
     *
     *
     * @param array $filters - Filter parameters
     * @return Builder - Filtered query builder
     *********************************************************************/
    private function buildBaseQuery(array $filters): Builder
    {
        return MarketplacePayment::query()
            // Apply date filter
            ->when(
                !empty($filters['preset_range']) || !empty($filters['start_date']),
                fn($q) => $this->applyDateFilter($q, $filters)
            )
            // Apply status filter
            ->when(
                !empty($filters['status']),
                fn($q) => $q->status($filters['status'])
            )
            // Apply search filter
            ->when(
                !empty($filters['searchTerm']),
                fn($q) => $q->search($filters['searchTerm'])
            );
    }




    /*********************************************************************
     * CALCULATE TOTALS
     *********************************************************************
     *
     * Calculates status-wise totals from the filtered base query.
     *
     *
     * @param Builder $query - Filtered query builder
     * @return array - Status-wise totals
     *********************************************************************/
    private function calculateTotals(Builder $query): array
    {
        $statuses = PaymentStatus::filterableValues();

        // 1) Get status-wise totals as before
        $statusTotals = (clone $query)
            ->whereIn('status', $statuses)
            ->selectRaw('status, SUM(payment_amount) as total')
            ->groupBy('status')
            ->pluck('total', 'status')
            ->toArray();

        // 2) Get grand total
        $grandTotal = (clone $query)->sum('payment_amount');

        // 3) Fill all statuses with 0, merge in real totals, then add grand total
        return array_merge(
            array_fill_keys($statuses, 0),
            $statusTotals,
            ['total' => $grandTotal]
        );
    }



    /*********************************************************************
     * GET PAGINATED RESULTS
     *********************************************************************
     *
     * Gets paginated results with relationships from the base query.
     *
     * @param Builder $query - Filtered query builder
     * @param array $filters - Filter parameters
     * @return LengthAwarePaginator - Paginated results
     *********************************************************************/
    private function getPaginatedResults(Builder $query, array $filters): LengthAwarePaginator
    {
        return $query
            ->with(['user', 'order'])
            ->withCount(['order' => fn($q) => $q->withCount('orderItems')])
            ->sort(
                $filters['sortField'] ?? 'id',
                $filters['sortOrder'] ?? 'desc'
            )
            ->paginate($filters['perPage'] ?? 15)
            ->withQueryString();
    }
}
